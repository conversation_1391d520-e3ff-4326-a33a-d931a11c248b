import { Link } from "wouter";
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { usePayments } from "@/hooks/usePayments";
import { useInvoices } from "@/hooks/useInvoices";
import { useReceivedPayments } from "@/hooks/useReceivedPayments";
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer } from "recharts";

const Home = () => {
  // Fetch data for dashboard statistics
  const { payments, approvedPayments, sentPayments, remittedPayments, isLoading: paymentsLoading } = usePayments();
  const { invoices, openInvoices, paidInvoices, remittedInvoices, isLoading: invoicesLoading } = useInvoices();
  const { receivedPayments, unlinkedPayments, linkedPayments, remittedPayments: remittedReceivedPayments, isLoading: receivedPaymentsLoading } = useReceivedPayments();

  // Calculate statistics
  const totalOutgoingAmount = payments.reduce((sum, p) => sum + p.amount, 0);
  const totalIncomingAmount = receivedPayments.reduce((sum, p) => sum + p.amount, 0);
  const totalPendingInvoicesAmount = openInvoices.reduce((sum, i) => sum + i.amount, 0);

  const isLoading = paymentsLoading || invoicesLoading || receivedPaymentsLoading;

  // Data for payment status pie chart
  const paymentStatusData = [
    { name: 'Not Approved', value: payments.length - approvedPayments.length - sentPayments.length - remittedPayments.length, color: '#f1c40f' },
    { name: 'Approved', value: approvedPayments.length - sentPayments.length - remittedPayments.length, color: '#2ecc71' },
    { name: 'Sent', value: sentPayments.length - remittedPayments.length, color: '#3498db' },
    { name: 'Remitted', value: remittedPayments.length, color: '#9b59b6' }
  ].filter(item => item.value > 0);

  // Data for invoice status pie chart
  const invoiceStatusData = [
    { name: 'Open', value: openInvoices.length, color: '#f1c40f' },
    { name: 'Paid', value: paidInvoices.length - remittedInvoices.length, color: '#2ecc71' },
    { name: 'Remitted', value: remittedInvoices.length, color: '#9b59b6' }
  ].filter(item => item.value > 0);

  // Data for received payments status
  const receivedPaymentStatusData = [
    { name: 'Unlinked', value: unlinkedPayments.length, color: '#f1c40f' },
    { name: 'Linked', value: linkedPayments.length - remittedReceivedPayments.length, color: '#2ecc71' },
    { name: 'Remitted', value: remittedReceivedPayments.length, color: '#9b59b6' }
  ].filter(item => item.value > 0);

  // Payment flow data (outgoing vs incoming)
  const paymentFlowData = [
    { name: 'Outgoing', amount: totalOutgoingAmount },
    { name: 'Incoming', amount: totalIncomingAmount }
  ];

  return (
    <div className="container mx-auto p-6">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">ERP/SAP Stablecoin Payment Layer</h1>
        <div className="text-gray-600 mb-8">
          Streamline your complex financial workflows through advanced transaction processing and intelligent document handling.
        </div>

        {/* Dashboard Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Outgoing Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <div className="h-8 w-24 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>${totalOutgoingAmount.toLocaleString()}</>
                )}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                {isLoading ? (
                  <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>{payments.length} payment(s) processed</>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Incoming Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <div className="h-8 w-24 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>${totalIncomingAmount.toLocaleString()}</>
                )}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                {isLoading ? (
                  <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>{receivedPayments.length} payment(s) received</>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Open Invoices</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <div className="h-8 w-24 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>${totalPendingInvoicesAmount.toLocaleString()}</>
                )}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                {isLoading ? (
                  <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>{openInvoices.length} invoice(s) pending</>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">Remittance Files</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? (
                  <div className="h-8 w-24 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>{remittedPayments.length + remittedReceivedPayments.length}</>
                )}
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                {isLoading ? (
                  <div className="h-4 w-32 bg-gray-200 animate-pulse rounded"></div>
                ) : (
                  <>Total remittances generated</>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts Section */}
        <div className="mb-10">
          <h2 className="text-xl font-semibold mb-6">Financial Overview</h2>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Payment Flow Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Flow</CardTitle>
                <CardDescription>Total outgoing vs. incoming payments</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="h-64 w-full bg-gray-100 animate-pulse rounded flex items-center justify-center">
                    <span className="text-gray-400">Loading chart data...</span>
                  </div>
                ) : paymentFlowData.some(d => d.amount > 0) ? (
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={paymentFlowData}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip formatter={(value) => `$${value.toLocaleString()}`} />
                        <Legend />
                        <Bar dataKey="amount" name="Amount (USD)" fill="#3498db" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="h-64 w-full bg-gray-50 rounded flex items-center justify-center">
                    <span className="text-gray-400">No payment data available</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Status</CardTitle>
                <CardDescription>Distribution of payment workflow statuses</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="h-64 w-full bg-gray-100 animate-pulse rounded flex items-center justify-center">
                    <span className="text-gray-400">Loading chart data...</span>
                  </div>
                ) : paymentStatusData.length > 0 ? (
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={paymentStatusData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {paymentStatusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => value} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="h-64 w-full bg-gray-50 rounded flex items-center justify-center">
                    <span className="text-gray-400">No payment data available</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Invoice Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Invoice Status</CardTitle>
                <CardDescription>Distribution of invoice statuses</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="h-64 w-full bg-gray-100 animate-pulse rounded flex items-center justify-center">
                    <span className="text-gray-400">Loading chart data...</span>
                  </div>
                ) : invoiceStatusData.length > 0 ? (
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={invoiceStatusData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {invoiceStatusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => value} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="h-64 w-full bg-gray-50 rounded flex items-center justify-center">
                    <span className="text-gray-400">No invoice data available</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Received Payment Status */}
            <Card>
              <CardHeader>
                <CardTitle>Received Payment Status</CardTitle>
                <CardDescription>Distribution of received payment statuses</CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="h-64 w-full bg-gray-100 animate-pulse rounded flex items-center justify-center">
                    <span className="text-gray-400">Loading chart data...</span>
                  </div>
                ) : receivedPaymentStatusData.length > 0 ? (
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={receivedPaymentStatusData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {receivedPaymentStatusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => value} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                ) : (
                  <div className="h-64 w-full bg-gray-50 rounded flex items-center justify-center">
                    <span className="text-gray-400">No received payment data available</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        <Separator className="my-8" />


      </div>
    </div>
  );
};

export default Home;
